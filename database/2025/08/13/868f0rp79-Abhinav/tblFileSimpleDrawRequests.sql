CREATE TABLE `tblFileSimpleDrawRequests` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `LMRId` INT UNSIGNED NOT NULL,
    `status` ENUM('pending', 'approved', 'rejected') NOT NULL,
    `submittedAt` DATETIME NOT NULL,
    `requestedAmount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `approvedAmount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `drawFee` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `wireAmount` DECIMAL(15,2) NULL DEFAULT NULL,
    `wireSentDate` DATE NULL DEFAULT NULL,
    `createdAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX IX_SimpleDrawRequests_LMRId (LMRId),
    CONSTRAINT FK_SimpleDrawRequests_File FOREIGN KEY (LMRId)
        REFERENCES tblFile(LMRId) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci;
