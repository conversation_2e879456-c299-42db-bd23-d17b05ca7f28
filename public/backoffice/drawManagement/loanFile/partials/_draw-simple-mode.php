
<?php
    use models\standard\Strings;
    use models\lendingwise\tblFileSimpleDrawRequests;

    $drawsData = tblFileSimpleDrawRequests::GetAll(['LMRId' => $LMRId]);
    $drawsData = $drawsData ?? [];
    $rehabPercentFinanced = Strings::replaceCommaValues($fileHMLONewLoanInfo['rehabCostPercentageFinanced']);
    $drawFee = $templateSettings->drawFee ?? 0;

?>

<div class="card card-custom card-stretch d-flex p-0 drawHistoryCard">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                Draw Request History
            </h3>
        </div>
        <div class="card-toolbar">
            <span onclick="addSimpleDrawRow()"
                  class="btn btn-sm btn-success btn-text-primary btn-icon ml-2 tooltipClass cursor-pointer"
                  title="Click to add new row">
                <i class="icon-md fas fa-plus"></i>
            </span>
            <a href="javascript:void(0);"
                class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                data-card-tool="toggle" data-section="drawHistoryCard" data-toggle="tooltip" data-placement="top"
                title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>

    <div class="card-body p-2">
        <div class="row">
            <div class="col-12">
                <div class="work-table">
                    <table class="table table-striped table-hover mb-0" id="simpleDrawTable">
                        <thead>
                            <tr>
                                <th style="width: 12%;">Status</th>
                                <th style="width: 12%;">Submission Date</th>
                                <th style="width: 12%;">Amount Requested</th>
                                <th style="width: 12%;">Amount Approved</th>
                                <th style="width: 10%;">Rehab% Financed</th>
                                <th style="width: 12%;">Draw Fee</th>
                                <th style="width: 12%;">Wire Amount</th>
                                <th style="width: 12%;">Wire Sent Date</th>
                                <th style="width: 6%;">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="simpleDrawTableBody">
                            <?php if (!empty($drawsData)): ?>
                                <?php foreach ($drawsData as $row): ?>
                                <tr data-row-id="<?= $row->id ?? 'new' ?>">
                                    <td>
                                        <select class="form-control" name="status" data-field="status">
                                            <option value="pending" <?= ($row->status ?? '') === 'pending' ? 'selected' : '' ?>>Pending</option>
                                            <option value="approved" <?= ($row->status ?? '') === 'approved' ? 'selected' : '' ?>>Approved</option>
                                            <option value="rejected" <?= ($row->status ?? '') === 'rejected' ? 'selected' : '' ?>>Rejected</option>
                                        </select>
                                    </td>
                                    <td>
                                        <input type="text"
                                               class="form-control dateClass"
                                               name="submittedAt"
                                               data-field="submittedAt"
                                               value="<?= isset($row->submittedAt) ? date('m/d/Y', strtotime($row->submittedAt)) : '' ?>"
                                               placeholder="mm/dd/yyyy">
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="requestedAmount"
                                                   data-field="requestedAmount"
                                                   value="<?= $row->requestedAmount ?? '0.00' ?>"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="approvedAmount"
                                                   data-field="approvedAmount"
                                                   value="<?= $row->approvedAmount ?? '0.00' ?>"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-light-primary"><?= $rehabPercentFinanced . '%'; ?></span>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="drawFee"
                                                   data-field="drawFee"
                                                   value="<?= $row->drawFee ?? '0.00' ?>"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="wireAmount"
                                                   data-field="wireAmount"
                                                   value="<?= $row->wireAmount ?? '' ?>"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00"
                                                   disabled>
                                        </div>
                                    </td>
                                    <td>
                                        <input type="text"
                                               class="form-control dateClass"
                                               name="wireSentDate"
                                               data-field="wireSentDate"
                                               value="<?= isset($row->wireSentDate) ? date('m/d/Y', strtotime($row->wireSentDate)) : '' ?>"
                                               placeholder="mm/dd/yyyy">
                                    </td>
                                    <td>
                                        <span onclick="removeSimpleDrawRow(this)"
                                              class="btn btn-sm btn-danger btn-text-primary btn-icon tooltipClass cursor-pointer"
                                              title="Click to remove row">
                                            <i class="icon-md fas fa-minus-circle"></i>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <!-- Empty row template when no data exists -->
                                <tr data-row-id="new">
                                    <td>
                                        <select class="form-control" name="status" data-field="status">
                                            <option value="pending" selected>Pending</option>
                                            <option value="approved">Approved</option>
                                            <option value="rejected">Rejected</option>
                                        </select>
                                    </td>
                                    <td>
                                        <input type="text"
                                               class="form-control dateClass"
                                               name="submittedAt"
                                               data-field="submittedAt"
                                               placeholder="mm/dd/yyyy">
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="requestedAmount"
                                                   data-field="requestedAmount"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="approvedAmount"
                                                   data-field="approvedAmount"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-light-primary"><?= $rehabPercentFinanced . '%'; ?></span>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="drawFee"
                                                   data-field="drawFee"
                                                   step="0.01"
                                                   min="0"
                                                   value="<?= $drawFee; ?>"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="wireAmount"
                                                   data-field="wireAmount"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00"
                                                   disabled>
                                        </div>
                                    </td>
                                    <td>
                                        <input type="text"
                                               class="form-control dateClass"
                                               name="wireSentDate"
                                               data-field="wireSentDate"
                                               placeholder="mm/dd/yyyy">
                                    </td>
                                    <td>
                                        <span onclick="removeSimpleDrawRow(this)"
                                              class="btn btn-sm btn-danger btn-text-primary btn-icon tooltipClass cursor-pointer"
                                              title="Click to remove row">
                                            <i class="icon-md fas fa-minus-circle"></i>
                                        </span>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="row mt-10">
            <div class="col-12 text-center">
                <button type="button" class="btn btn-primary" onclick="saveSimpleDrawRequests()">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
function addSimpleDrawRow() {
    const tableBody = document.getElementById('simpleDrawTableBody');
    const rehabPercent = '<?= $rehabPercentFinanced . '%'; ?>';

    const newRow = document.createElement('tr');
    newRow.setAttribute('data-row-id', 'new');
    newRow.innerHTML = `
        <td>
            <select class="form-control" name="status" data-field="status">
                <option value="pending" selected>Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
            </select>
        </td>
        <td>
            <input type="text"
                   class="form-control dateClass"
                   name="submittedAt"
                   data-field="submittedAt"
                   placeholder="mm/dd/yyyy">
        </td>
        <td>
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">$</span>
                </div>
                <input type="number"
                       class="form-control"
                       name="requestedAmount"
                       data-field="requestedAmount"
                       step="0.01"
                       min="0"
                       placeholder="0.00">
            </div>
        </td>
        <td>
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">$</span>
                </div>
                <input type="number"
                       class="form-control"
                       name="approvedAmount"
                       data-field="approvedAmount"
                       step="0.01"
                       min="0"
                       placeholder="0.00">
            </div>
        </td>
        <td>
            <span class="badge badge-light-primary">${rehabPercent}</span>
        </td>
        <td>
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">$</span>
                </div>
                <input type="number"
                       class="form-control"
                       name="drawFee"
                       data-field="drawFee"
                       step="0.01"
                       min="0"
                       value="<?= $drawFee; ?>"
                       placeholder="0.00">
            </div>
        </td>
        <td>
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">$</span>
                </div>
                <input type="number"
                       class="form-control"
                       name="wireAmount"
                       data-field="wireAmount"
                       step="0.01"
                       min="0"
                       placeholder="0.00"
                       disabled>
            </div>
        </td>
        <td>
            <input type="text"
                   class="form-control dateClass"
                   name="wireSentDate"
                   data-field="wireSentDate"
                   placeholder="mm/dd/yyyy">
        </td>
        <td>
            <span onclick="removeSimpleDrawRow(this)"
                  class="btn btn-sm btn-danger btn-text-primary btn-icon tooltipClass cursor-pointer"
                  title="Click to remove row">
                <i class="icon-md fas fa-minus-circle"></i>
            </span>
        </td>
    `;

    tableBody.appendChild(newRow);

    // Initialize date picker for new date inputs
    $(newRow).find('.dateClass').each(function() {
        dateClass.init($(this));
    });
}

function removeSimpleDrawRow(element) {
    const row = element.closest('tr');
    const tableBody = document.getElementById('simpleDrawTableBody');
    const rowId = row.getAttribute('data-row-id');

    // If this is an existing record (not 'new'), ask for confirmation and delete from database
    if (rowId && rowId !== 'new') {
        if (!confirm('Are you sure you want to delete this draw request? This action cannot be undone.')) {
            return;
        }

        // Send delete request to API
        $.ajax({
            url: '/backoffice/api_v2/draw_management/SimpleDrawRequests/',
            type: 'DELETE',
            contentType: 'application/json',
            data: JSON.stringify({ id: rowId }),
            success: function(response) {
                if (response.success) {
                    toastrNotification('Draw request deleted successfully!', 'success');
                    row.remove();

                    // If no rows left, add an empty row
                    if (tableBody.children.length === 0) {
                        addSimpleDrawRow();
                    }
                } else {
                    toastrNotification('Error: ' + (response.message || 'Failed to delete draw request'), 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('Delete Error:', error);
                let errorMsg = 'An error occurred while deleting the record.';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }

                toastrNotification(errorMsg, 'error');
            }
        });
    } else {
        // For new rows, just remove from UI
        if (tableBody.children.length > 1) {
            row.remove();
        } else {
            // Clear the values instead of removing the last row
            $(row).find('input, select').val('');
            $(row).find('select[name="status"]').val('pending');
        }
    }
}

function saveSimpleDrawRequests() {
    const tableBody = document.getElementById('simpleDrawTableBody');
    const rows = tableBody.querySelectorAll('tr');
    const drawRequests = [];
    let hasErrors = false;

    // Collect data from all rows
    rows.forEach((row, index) => {
        const rowData = {
            id: row.getAttribute('data-row-id'),
            status: row.querySelector('[data-field="status"]').value,
            submittedAt: row.querySelector('[data-field="submittedAt"]').value,
            requestedAmount: row.querySelector('[data-field="requestedAmount"]').value,
            approvedAmount: row.querySelector('[data-field="approvedAmount"]').value,
            drawFee: row.querySelector('[data-field="drawFee"]').value,
            wireAmount: row.querySelector('[data-field="wireAmount"]').value,
            wireSentDate: row.querySelector('[data-field="wireSentDate"]').value
        };

        // Basic validation
        if (!rowData.status || !rowData.submittedAt || !rowData.requestedAmount) {
            toastrNotification(`Row ${index + 1}: Status, Submission Date, and Amount Requested are required`, 'error');
            hasErrors = true;
            return;
        }

        // Only add rows with meaningful data
        if (rowData.status !== 'pending' || rowData.submittedAt || parseFloat(rowData.requestedAmount) > 0) {
            drawRequests.push(rowData);
        }
    });

    if (hasErrors) {
        return;
    }

    if (drawRequests.length === 0) {
        toastrNotification('No data to save', 'warning');
        return;
    }

    // Prepare data for API
    const requestData = {
        LMRId: <?= $LMRId ?>,
        drawRequests: drawRequests
    };

    // Disable save button and show loading state
    const saveBtn = document.querySelector('button[onclick="saveSimpleDrawRequests()"]');
    const originalText = saveBtn.textContent;
    saveBtn.disabled = true;
    saveBtn.textContent = 'Saving...';

    // Send AJAX request
    $.ajax({
        url: '/backoffice/api_v2/draw_management/SimpleDrawRequests/',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(requestData),
        success: function(response) {
            if (response.success) {
                toastrNotification('Simple draw requests saved successfully!', 'success');
                // Reload the page to show updated data
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                toastrNotification('Error: ' + (response.message || 'Failed to save draw requests'), 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            let errorMsg = 'An error occurred while saving data. Please try again.';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMsg = xhr.responseJSON.message;
            } else if (xhr.responseText) {
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    errorMsg = errorResponse.message || errorMsg;
                } catch (e) {
                    // Use default error message
                }
            }

            toastrNotification(errorMsg, 'error');
        },
        complete: function() {
            // Re-enable save button
            saveBtn.disabled = false;
            saveBtn.textContent = originalText;
        }
    });
}

$(document).ready(function() {
    $('.dateClass').each(function() {
        dateClass.init($(this));
    });
});
</script>
