
<?php
    use models\standard\Strings;
    use models\lendingwise\tblFileSimpleDrawRequests;

    $drawsData = tblFileSimpleDrawRequests::Get(['LMRId' => $LMRId]);
    $drawsData = $drawsData ?? [];
    $rehabPercentFinanced = Strings::replaceCommaValues($fileHMLONewLoanInfo['rehabCostPercentageFinanced']);
    $drawFee = $templateSettings->drawFee ?? 0;

    
?>

<div class="card card-custom card-stretch d-flex p-0 drawHistoryCard">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                Draw Request History
            </h3>
        </div>
        <div class="card-toolbar">
            <span onclick="addSimpleDrawRow()"
                  class="btn btn-sm btn-success btn-text-primary btn-icon ml-2 tooltipClass cursor-pointer"
                  title="Click to add new row">
                <i class="icon-md fas fa-plus"></i>
            </span>
            <a href="javascript:void(0);"
                class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                data-card-tool="toggle" data-section="drawHistoryCard" data-toggle="tooltip" data-placement="top"
                title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>

    <div class="card-body p-2">
        <div class="row">
            <div class="col-12">
                <div class="work-table">
                    <table class="table table-striped table-hover mb-0" id="simpleDrawTable">
                        <thead>
                            <tr>
                                <th style="width: 12%;">Status</th>
                                <th style="width: 12%;">Submission Date</th>
                                <th style="width: 12%;">Amount Requested</th>
                                <th style="width: 12%;">Amount Approved</th>
                                <th style="width: 10%;">Rehab% Financed</th>
                                <th style="width: 12%;">Draw Fee</th>
                                <th style="width: 12%;">Wire Amount</th>
                                <th style="width: 12%;">Wire Sent Date</th>
                                <th style="width: 6%;">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="simpleDrawTableBody">
                            <?php if (!empty($drawsData)): ?>
                                <?php foreach ($drawsData as $row): ?>
                                <tr data-row-id="<?= $row->id ?? 'new' ?>">
                                    <td>
                                        <select class="form-control" name="status" data-field="status">
                                            <option value="pending" <?= ($row->status ?? '') === 'pending' ? 'selected' : '' ?>>Pending</option>
                                            <option value="approved" <?= ($row->status ?? '') === 'approved' ? 'selected' : '' ?>>Approved</option>
                                            <option value="rejected" <?= ($row->status ?? '') === 'rejected' ? 'selected' : '' ?>>Rejected</option>
                                        </select>
                                    </td>
                                    <td>
                                        <input type="text"
                                               class="form-control dateClass"
                                               name="submittedAt"
                                               data-field="submittedAt"
                                               value="<?= isset($row->submittedAt) ? date('m/d/Y', strtotime($row->submittedAt)) : '' ?>"
                                               placeholder="mm/dd/yyyy">
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="requestedAmount"
                                                   data-field="requestedAmount"
                                                   value="<?= $row->requestedAmount ?? '0.00' ?>"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="approvedAmount"
                                                   data-field="approvedAmount"
                                                   value="<?= $row->approvedAmount ?? '0.00' ?>"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-light-primary"><?= $rehabPercentFinanced . '%'; ?></span>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="drawFee"
                                                   data-field="drawFee"
                                                   value="<?= $row->drawFee ?? '0.00' ?>"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="wireAmount"
                                                   data-field="wireAmount"
                                                   value="<?= $row->wireAmount ?? '' ?>"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <input type="text"
                                               class="form-control dateClass"
                                               name="wireSentDate"
                                               data-field="wireSentDate"
                                               value="<?= isset($row->wireSentDate) ? date('m/d/Y', strtotime($row->wireSentDate)) : '' ?>"
                                               placeholder="mm/dd/yyyy">
                                    </td>
                                    <td>
                                        <span onclick="removeSimpleDrawRow(this)"
                                              class="btn btn-sm btn-danger btn-text-primary btn-icon tooltipClass cursor-pointer"
                                              title="Click to remove row">
                                            <i class="icon-md fas fa-minus-circle"></i>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <!-- Empty row template when no data exists -->
                                <tr data-row-id="new">
                                    <td>
                                        <select class="form-control" name="status" data-field="status">
                                            <option value="pending" selected>Pending</option>
                                            <option value="approved">Approved</option>
                                            <option value="rejected">Rejected</option>
                                        </select>
                                    </td>
                                    <td>
                                        <input type="text"
                                               class="form-control dateClass"
                                               name="submittedAt"
                                               data-field="submittedAt"
                                               placeholder="mm/dd/yyyy">
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="requestedAmount"
                                                   data-field="requestedAmount"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="approvedAmount"
                                                   data-field="approvedAmount"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-light-primary"><?= $rehabPercentFinanced . '%'; ?></span>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="drawFee"
                                                   data-field="drawFee"
                                                   step="0.01"
                                                   min="0"
                                                   value="<?= $drawFee; ?>"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number"
                                                   class="form-control"
                                                   name="wireAmount"
                                                   data-field="wireAmount"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00">
                                        </div>
                                    </td>
                                    <td>
                                        <input type="text"
                                               class="form-control dateClass"
                                               name="wireSentDate"
                                               data-field="wireSentDate"
                                               placeholder="mm/dd/yyyy">
                                    </td>
                                    <td>
                                        <span onclick="removeSimpleDrawRow(this)"
                                              class="btn btn-sm btn-danger btn-text-primary btn-icon tooltipClass cursor-pointer"
                                              title="Click to remove row">
                                            <i class="icon-md fas fa-minus-circle"></i>
                                        </span>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="row mt-10">
            <div class="col-12 text-center">
                <button type="button" class="btn btn-primary" onclick="saveSimpleDrawRequests()">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
function addSimpleDrawRow() {
    const tableBody = document.getElementById('simpleDrawTableBody');
    const rehabPercent = '<?= $rehabPercentFinanced . '%'; ?>';

    const newRow = document.createElement('tr');
    newRow.setAttribute('data-row-id', 'new');
    newRow.innerHTML = `
        <td>
            <select class="form-control" name="status" data-field="status">
                <option value="pending" selected>Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
            </select>
        </td>
        <td>
            <input type="text"
                   class="form-control dateClass"
                   name="submittedAt"
                   data-field="submittedAt"
                   placeholder="mm/dd/yyyy">
        </td>
        <td>
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">$</span>
                </div>
                <input type="number"
                       class="form-control"
                       name="requestedAmount"
                       data-field="requestedAmount"
                       step="0.01"
                       min="0"
                       placeholder="0.00">
            </div>
        </td>
        <td>
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">$</span>
                </div>
                <input type="number"
                       class="form-control"
                       name="approvedAmount"
                       data-field="approvedAmount"
                       step="0.01"
                       min="0"
                       placeholder="0.00">
            </div>
        </td>
        <td>
            <span class="badge badge-light-primary">${rehabPercent}</span>
        </td>
        <td>
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">$</span>
                </div>
                <input type="number"
                       class="form-control"
                       name="drawFee"
                       data-field="drawFee"
                       step="0.01"
                       min="0"
                       value="<?= $drawFee; ?>"
                       placeholder="0.00">
            </div>
        </td>
        <td>
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">$</span>
                </div>
                <input type="number"
                       class="form-control"
                       name="wireAmount"
                       data-field="wireAmount"
                       step="0.01"
                       min="0"
                       placeholder="0.00">
            </div>
        </td>
        <td>
            <input type="text"
                   class="form-control dateClass"
                   name="wireSentDate"
                   data-field="wireSentDate"
                   placeholder="mm/dd/yyyy">
        </td>
        <td>
            <span onclick="removeSimpleDrawRow(this)"
                  class="btn btn-sm btn-danger btn-text-primary btn-icon tooltipClass cursor-pointer"
                  title="Click to remove row">
                <i class="icon-md fas fa-minus-circle"></i>
            </span>
        </td>
    `;

    tableBody.appendChild(newRow);

    // Initialize date picker for new date inputs
    $(newRow).find('.dateClass').each(function() {
        dateClass.init($(this));
    });
}

function removeSimpleDrawRow(element) {
    const row = element.closest('tr');
    const tableBody = document.getElementById('simpleDrawTableBody');

    if (tableBody.children.length > 1) {
        row.remove();
    } else {
        $(row).find('input, select').val('');
        $(row).find('select[name="status"]').val('pending');
    }
}

$(document).ready(function() {
    $('.dateClass').each(function() {
        dateClass.init($(this));
    });
});
</script>
