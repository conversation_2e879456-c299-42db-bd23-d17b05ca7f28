
<?php
    use models\standard\Strings;
    use models\lendingwise\tblFileSimpleDrawRequests;

    $drawsData = tblFileSimpleDrawRequests::Get(['LMRId' => $LMRId]);
    $drawsData = $drawsData ?? [];
    $rehabPercentFinanced = Strings::replaceCommaValues($fileHMLONewLoanInfo['rehabCostPercentageFinanced']);

?>

<div class="card card-custom card-stretch d-flex p-0 drawHistoryCard">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                Draw Request History
            </h3>
        </div>
        <div class="card-toolbar">
            <a href="javascript:void(0);"
                class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                data-card-tool="toggle" data-section="drawHistoryCard" data-toggle="tooltip" data-placement="top"
                title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>

    <div class="card-body p-2">
        <div class="row">
            <div class="col-12">
                <div class="work-table">
                    <table class="table table-striped table-hover mb-0">
                        <thead>
                            <tr>
                                <th>&emsp;&emsp;Status</th>
                                <th>Submission Date</th>
                                <th>Amount Requested</th>
                                <th>Amount Approved</th>
                                <th>Rehab% Financed</th>
                                <th>Draw Fee</th>
                                <th>Wire Amount</th>
                                <th>Wire Sent Date</th>
                                <th style="width: 50px"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($drawsData as $idx => $row): ?>
                            <tr>
                                <td>
                                    <span class="badge badge-light-primary"><?= ++$idx; ?></span>
                                </td>
                                <td><?= $row->submittedAt ? date('m/d/Y', strtotime($row->submittedAt)) : '-'; ?></td>
                                <td>
                                    <?= $row->requestedAmount ? '$' . number_format($row->requestedAmount, 2) : '-'; ?>
                                </td>
                                <td>
                                    <?= $row->approvedAmount ? '$' . number_format($row->approvedAmount, 2) : '-'; ?>
                                </td>
                                <td>
                                    <?= $rehabPercentFinanced . '%'; ?>
                                </td>
                                <td>
                                    <?= $row->drawFee ? '$' . number_format($row->drawFee, 2) : '-'; ?>
                                </td>
                                <td>
                                    <?= $row->wireAmount ? '$' . number_format($row->wireAmount, 2) : '-'; ?>
                                </td>
                                <td>
                                    <?php if ($row->wireSentDate): ?>
                                        <?= date('M j, Y', strtotime($row->wireSentDate)); ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <i class="fa fa-edit"></i>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
