<?php

use models\composite\oDrawManagement\DrawRequestManager;
use models\composite\oDrawManagement\DrawRequest;
use models\composite\oDrawManagement\SowTemplateManager;
use models\PageVariables;
use models\standard\Strings;
use models\Controllers\Base\generateWebformLinks;

global $LMRId, $PCID;

try {
    $templateManager = SowTemplateManager::forProcessingCompany($PCID);
    $templateSettings = $templateManager->getTemplate();
    $drawRequestManager = DrawRequestManager::forLoanFile($LMRId);
    $requestData = $drawRequestManager->getDrawRequest();
    $categoriesData = [];

    if(isset($requestData->status) && $requestData->status !== DrawRequest::STATUS_NEW ) {
        $categoriesData = $requestData->getAllCategories();
    }
} catch (Exception $e) {
    echo "Error loading draw request data: " . htmlspecialchars($e->getMessage()) . " -->";
    return;
}

$displayStatus = '';
$displayStatusClass = '';
$disableAction = false;
if($requestData && $requestData->status) {
    switch($requestData->status) {
        case DrawRequest::STATUS_NEW:
            $disableAction = true;
            break;
        case DrawRequest::STATUS_PENDING:
            $displayStatus = 'Submitted';
            $displayStatusClass = 'bg-warning';
            break;
        case DrawRequest::STATUS_APPROVED:
            $displayStatus = $requestData->isDrawRequest ? 'Draw Request Approved' : 'Draw 1 Pending';
            $displayStatusClass = 'bg-success';
            $disableAction = true;
            break;
        case DrawRequest::STATUS_REJECTED:
            $displayStatus = 'Rejected';
            $displayStatusClass = 'bg-danger';
            $disableAction = true;
            break;
    }
}

$disableAction = $disableAction || !PageVariables::$allowToManageDraws;
generateWebformLinks::init($LMRId);
$webFormLink = generateWebformLinks::$submitReviseSow;

Strings::includeMyScript([
    '/backoffice/drawManagement/js/utils/ApiClient.js',
    '/backoffice/drawManagement/js/drawRequest.js',
    '/backoffice/drawManagement/js/utils/Validator.js',
    '/backoffice/drawManagement/js/utils/DataMapper.js',
    '/backoffice/drawManagement/js/loanFileDrawRequest.js'
]);

Strings::includeMyCSS(['/backoffice/drawManagement/loanFile/css/drawManagement.css']);

?>
<div class="card card-body p-0">
    <?php if (!$templateSettings->enableSimpleMode): ?>
        <div class="card card-custom card-stretch d-flex p-0 drawManagementCard">
            <div class="card-header card-header-tabs-line bg-gray-100">
                <div class="card-title">
                    <h3 class="card-label">
                        Draw Management
                    </h3>
                </div>
                <div class="card-toolbar">
                    <a href="javascript:void(0);"
                        class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                        data-card-tool="toggle" data-section="drawManagementCard" data-toggle="tooltip" data-placement="top"
                        title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </a>
                </div>
            </div>

            <div class="card-body p-2">
                <div class="row">
                    <div class="col-12">
                        <div class="row">
                            <div class="d-flex justify-content-start mb-4 mt-2 col-md-6">
                                <?php if($displayStatus) { ?>
                                    <span class="badge d-inline-flex align-items-center <?= $displayStatusClass; ?>">
                                        <i class="fas <?= $requestData->status === DrawRequest::STATUS_APPROVED ? 'fa-check-circle' : 'fa-info-circle'; ?> mr-2 text-dark"></i>
                                        <?= $displayStatus; ?></span>
                                <?php } ?>
                            </div>
                            <div class="d-flex justify-content-end mb-4 mt-2 col-md-6">
                                <div class="d-flex align-items-end mr-3">
                                    <a href="<?= $webFormLink ?>" target="_blank" class="btn btn-outline-primary" id="drawRequestFormLink">
                                        Draw Request Form <i class="fas fa-external-link-alt fa-sm ml-2"></i>
                                    </a>
                                </div>
                            <div class="d-flex align-items-end">
                                <?php if($categoriesData): ?>
                                    <button id="exportTableBtn" class="btn btn-primary btn-sm" type="button">
                                        <i class="fas fa-download mr-2"></i>Export Table
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                        </div>
                        <div class="work-table">
                            <?php
                            if($requestData && isset($requestData->sowApproved) && $requestData->sowApproved):
                                require 'partials/_table-draw-request.php';
                            else:
                                require 'partials/_table-scope-of-work.php';
                            endif; ?>
                        </div>
                    </div>
                </div>
            </div>
                <?php if(!$disableAction): ?>
            <div class="row mb-5">
            <div class="col-12">
                <div class=" d-flex align-items-center">
                    <div class="col-4"></div>
                    <div class="col-2 d-flex align-items-center">
                        <label class="mr-2 font-weight-bold" for="status">Action:</label>
                        <select class="form-control input-sm statusAction" name="status" id="status">
                            <option <?= ($requestData->status ?? '') !== DrawRequest::STATUS_REJECTED ? 'selected' : ''; ?> value="<?= DrawRequest::STATUS_APPROVED; ?>">Accept</option>
                            <option <?= ($requestData->status ?? '') === DrawRequest::STATUS_REJECTED ? 'selected' : ''; ?> value="<?= DrawRequest::STATUS_REJECTED; ?>">Reject</option>
                        </select>
                    </div>
                    <div class="col-2 d-flex justify-content-left">
                        <button type="submit" name="btnSave" id="btnSave" class="btn btn-primary">Save</button>
                    </div>
                    <div class="col-4"></div>
                </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
    </div>

    <?php require 'drawSummary.php'; ?>
    <?php
    if($templateSettings->enableSimpleMode) {
        require 'partials/_draw-simple-mode.php';
    } else {
        require 'drawHistory.php';
    } ?>
</div>

<!-- Lender Notes Modal -->
<?php if(!$disableAction): ?>
<div class="modal fade lender-notes-modal" id="lenderNotesModal" tabindex="-1" role="dialog" aria-labelledby="lenderNotesModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="lenderNotesModalLabel">Lender Notes</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <textarea class="form-control lender-notes-textarea" id="lenderNotesTextarea" placeholder="Enter lender notes for this line item..."></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveLenderNote">Save Notes</button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
<div class="modal fade line-item-docs-modal" id="lineItemDocsModal" tabindex="-1" role="dialog" aria-labelledby="lineItemDocsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Line Item Documents</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                <table class="table table-hover table-bordered table-vertical-center">
                    <thead>
                        <tr class="text-center">
                            <th class="w-30">Document Name</th>
                            <th class="w-25">Preview</th>
                            <th>Uploaded By</th>
                            <th>Upload Date</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Docs Data will be populated dynamically -->
                    </tbody>
                </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
$(document).ready(function() {
    if (typeof DrawRequestUtils !== 'undefined') {
        DrawRequestUtils.config.allowUsersDeleteUploads = <?= $templateSettings->allowUsersDeleteUploads; ?>;
        DrawRequestUtils.config.allowUserManageDraws = <?= PageVariables::$allowToManageDraws; ?>;
        DrawRequestUtils.config.LMRId = <?= $LMRId; ?>;
    }
    if (typeof LoanFileDrawRequestManager !== 'undefined') {
        const drawRequestConfig = {
            $saveBtn: $('#btnSave'),
            sowApproved: parseInt(<?= isset($requestData->sowApproved) && $requestData->sowApproved ? '1' : '0'; ?>),
            statusApproved: '<?= DrawRequest::STATUS_APPROVED; ?>',
            statusRejected: '<?= DrawRequest::STATUS_REJECTED; ?>',
            LMRId: <?= $LMRId; ?>,
            currentStatus: '<?= $requestData->status ?? ''; ?>'
        };
        loanFileDrawRequestManager = new LoanFileDrawRequestManager(drawRequestConfig);
    } else {
        console.error('LoanFileDrawRequestManager class not found. Make sure the script is loaded.');
    }
});
</script>
