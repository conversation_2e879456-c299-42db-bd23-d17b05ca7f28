<?php

namespace pages\backoffice\api_v2\draw_management\SimpleDrawRequests;

use models\lendingwise\tblFileSimpleDrawRequests;
use models\standard\Dates;
use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;

/**
 * Class SimpleDrawRequests
 *
 * API endpoint for managing simple draw requests
 *
 * @package pages\backoffice\api_v2\draw_management\SimpleDrawRequests
 */
class SimpleDrawRequests extends DrawManagementApiBase
{
    /**
     * Handle POST requests to save simple draw requests
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = static::parseJsonInput();
        if (!$postData) return; // parseJsonInput handles error response

        static::executeWithErrorHandling(function() use ($postData) {
            // Validate required fields
            if (!isset($postData['LMRId']) || !isset($postData['drawRequests'])) {
                static::sendErrorResponse('Missing required fields: LMRId and drawRequests');
                return;
            }

            $LMRId = static::getLMRId($postData['LMRId']);
            $drawRequests = $postData['drawRequests'];

            // Validate loan file exists
            static::validateLoanFile($LMRId);

            // Validate draw requests data
            if (!is_array($drawRequests)) {
                static::sendErrorResponse('Draw requests must be an array');
                return;
            }

            // Save draw requests
            $result = static::saveSimpleDrawRequests($LMRId, $drawRequests);

            if ($result['success']) {
                static::sendSuccessResponse($result['data'], 'Simple draw requests saved successfully');
            } else {
                static::sendErrorResponse($result['message'] ?? 'Failed to save simple draw requests');
            }
        });
    }

    /**
     * Handle GET requests to fetch simple draw requests
     *
     * @return void
     */
    public static function Get(): void
    {
        parent::Init();

        $LMRId = static::getLMRId($_GET['LMRId'] ?? null);

        static::executeWithErrorHandling(function() use ($LMRId) {
            // Validate loan file exists
            static::validateLoanFile($LMRId);

            // Get simple draw requests
            $drawRequests = tblFileSimpleDrawRequests::GetAll(['LMRId' => $LMRId]);
            $drawRequests = $drawRequests ?? [];

            static::sendSuccessResponse($drawRequests, 'Simple draw requests fetched successfully');
        });
    }

    /**
     * Handle DELETE requests to remove simple draw requests
     *
     * @return void
     */
    public static function Delete(): void
    {
        parent::Init();

        $postData = static::parseJsonInput();
        if (!$postData) return; // parseJsonInput handles error response

        static::executeWithErrorHandling(function() use ($postData) {
            // Validate required fields
            if (!isset($postData['id'])) {
                static::sendErrorResponse('Missing required field: id');
                return;
            }

            $id = (int)$postData['id'];

            // Get the record to validate it exists and get LMRId for validation
            $drawRequest = tblFileSimpleDrawRequests::Get(['id' => $id]);
            if (!$drawRequest) {
                static::sendErrorResponse('Record not found');
                return;
            }

            // Validate loan file exists
            static::validateLoanFile($drawRequest->LMRId);

            // Delete the record
            $result = $drawRequest->Delete();

            if (!$result['error']) {
                static::sendSuccessResponse(['id' => $id], 'Simple draw request deleted successfully');
            } else {
                static::sendErrorResponse('Failed to delete record: ' . ($result['error_msg'] ?? 'Unknown error'));
            }
        });
    }

    /**
     * Save simple draw requests to database
     *
     * @param int $LMRId
     * @param array $drawRequests
     * @return array
     */
    private static function saveSimpleDrawRequests(int $LMRId, array $drawRequests): array
    {
        try {
            $savedRequests = [];
            $errors = [];

            foreach ($drawRequests as $index => $request) {
                $result = static::saveIndividualDrawRequest($LMRId, $request);

                if ($result['success']) {
                    $savedRequests[] = $result['data'];
                } else {
                    $errors[] = "Row " . ($index + 1) . ": " . $result['message'];
                }
            }

            if (!empty($errors)) {
                return [
                    'success' => false,
                    'message' => 'Some requests failed to save: ' . implode(', ', $errors),
                    'data' => $savedRequests
                ];
            }

            return [
                'success' => true,
                'data' => $savedRequests
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Database error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Save individual draw request
     *
     * @param int $LMRId
     * @param array $request
     * @return array
     */
    private static function saveIndividualDrawRequest(int $LMRId, array $request): array
    {
        try {
            // Validate required fields
            $requiredFields = ['status', 'submittedAt', 'requestedAmount'];
            foreach ($requiredFields as $field) {
                if (!isset($request[$field]) || $request[$field] === '') {
                    return [
                        'success' => false,
                        'message' => "Missing required field: {$field}"
                    ];
                }
            }

            // Check if this is an update or insert
            if (!empty($request['id']) && $request['id'] !== 'new') {
                // Update existing record
                $drawRequest = tblFileSimpleDrawRequests::Get(['id' => (int)$request['id']]);
                if (!$drawRequest) {
                    return [
                        'success' => false,
                        'message' => 'Record not found for update'
                    ];
                }
            } else {
                // Create new record
                $drawRequest = new tblFileSimpleDrawRequests();
                $drawRequest->LMRId = $LMRId;
            }

            // Set properties
            $drawRequest->status = $request['status'];
            $drawRequest->submittedAt = static::formatDateTime($request['submittedAt']);
            $drawRequest->requestedAmount = (float)($request['requestedAmount'] ?? 0);
            $drawRequest->approvedAmount = (float)($request['approvedAmount'] ?? 0);
            $drawRequest->drawFee = (float)($request['drawFee'] ?? 0);
            $drawRequest->wireAmount = !empty($request['wireAmount']) ? (float)$request['wireAmount'] : null;
            $drawRequest->wireSentDate = !empty($request['wireSentDate']) ? static::formatDate($request['wireSentDate']) : null;

            // Save the record
            $result = $drawRequest->Save();

            if (!$result['error']) {
                return [
                    'success' => true,
                    'data' => $drawRequest
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Database operation failed: ' . ($result['error_msg'] ?? 'Unknown error')
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error saving request: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Format date string for database
     *
     * @param string $dateString
     * @return string|null
     */
    private static function formatDate(string $dateString): ?string
    {
        if (empty($dateString)) {
            return null;
        }

        try {
            $date = \DateTime::createFromFormat('m/d/Y', $dateString);
            if ($date) {
                return $date->format('Y-m-d');
            }

            // Try alternative format
            $date = \DateTime::createFromFormat('Y-m-d', $dateString);
            if ($date) {
                return $date->format('Y-m-d');
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Format datetime string for database
     *
     * @param string $dateTimeString
     * @return string|null
     */
    private static function formatDateTime(string $dateTimeString): ?string
    {
        if (empty($dateTimeString)) {
            return null;
        }

        try {
            $date = \DateTime::createFromFormat('m/d/Y', $dateTimeString);
            if ($date) {
                return $date->format('Y-m-d H:i:s');
            }

            // Try alternative formats
            $date = \DateTime::createFromFormat('Y-m-d H:i:s', $dateTimeString);
            if ($date) {
                return $date->format('Y-m-d H:i:s');
            }

            $date = \DateTime::createFromFormat('Y-m-d', $dateTimeString);
            if ($date) {
                return $date->format('Y-m-d 00:00:00');
            }

            return date('Y-m-d H:i:s'); // Default to current datetime
        } catch (\Exception $e) {
            return date('Y-m-d H:i:s'); // Default to current datetime
        }
    }
}
